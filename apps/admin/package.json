{"name": "admin", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit", "clean": "rm -rf .next .turbo node_modules"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@next-starter/ui": "workspace:*", "lucide-react": "^0.511.0", "next": "^15.3.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "zod": "^3.25.28"}, "devDependencies": {"@next-starter/eslint-config": "workspace:*", "@next-starter/typescript-config": "workspace:*", "@tailwindcss/postcss": "^4.1.7", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "eslint": "^9.27.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "typescript": "5.8.2"}}